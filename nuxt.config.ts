import { buildSessionStorageOptionsFromEnvironment } from './modules/auth/lib/session-store'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import consoleStamp from 'console-stamp';
import config from './config';

const RELEASE = 'v0.9.7'

consoleStamp(console, {
    format: ':date(dd.mm.yyyy HH:MM:ss.l)'
});

// https://v3.nuxtjs.org/docs/directory-structure/nuxt.config
export default defineNuxtConfig({
    ssr: false,

    // @see ~/app/spa-loading-template.html
    // @see https://github.com/nuxt/assets/tree/main
    // spaLoadingTemplate: false,

    experimental: {
        // https://github.com/nuxt/nuxt/pull/19086
        emitRouteChunkError: 'automatic'
    },

    app: {
        baseURL: config.urls.base.prefix,
        head: {
            link: [{
                rel: 'icon', type: 'image/x-icon', href: `${config.urls.base.prefix}/favicon.ico`
            }]
        }
    },

    modules: ['./modules/auth', './modules/api', '@nuxtjs/tailwindcss', './modules/ui'],

    tailwindcss: {
        viewer: false,
        cssPath: '~~/assets/css/tailwind.css',
        configPath: '~~/modules/ui/tailwind.config.js'
        // exposeConfig: false,
        // config: {},
        // injectPosition: 0,
    },

    auth: {
        keycloak: {
            url: process.env.KEYCLOAK_URL,
            realm: process.env.KEYCLOAK_REALM,
            clientId: process.env.KEYCLOAK_CLIENT_ID,
            clientSecret: process.env.KEYCLOAK_CLIENT_SECRET
        },

        session: buildSessionStorageOptionsFromEnvironment(process.env as any)
    },

    components: {
        dirs: ['~/components'],
        global: false
    },

    runtimeConfig: {
        api: {
            clientName: 'DRK-Server-Portal',
            baseUrl: process.env.API_BASE_URL || 'https://demo-api.drkserver.org'
        },
        public: {
            environment: process.env.NODE_ENV,
            baseUrl: config.urls.base.url,
            drkLegacy: config.urls.portal.url,
            portalUrl: config.urls.portal.url,
            sentry: {
                enabled: !!process.env.SENTRY_DSN && (process.env.NODE_ENV ?? 'production') === 'production',
                dsn: process.env.SENTRY_DSN || null, // "https://<EMAIL>/41"
                tunnel: process.env.SENTRY_TUNNEL,
                release: RELEASE,
                environment: process.env.SENTRY_ENVIRONMENT || 'development',
                tracePropagationTarget: process.env.SENTRY_TRACE_PROPAGATION_TARGET
            }
        }
    },

    typescript: {
        shim: false,
        strict: false,
        typeCheck: false //process.env.NODE_ENV !== 'production'
    },

    postcss: {
        plugins: {
            'postcss-inline-svg': {}
        }
    },

    sourcemap: {
        server: true,
        client: true
    },

    vite: {
        build: {
            sourcemap: true
        },
        vue: {
            script: {
                defineModel: true,
                propsDestructure: true
            }
        },
        plugins: [
            sentryVitePlugin({
                authToken: process.env.SENTRY_AUTH_TOKEN,
                org: process.env.SENTRY_ORG,
                project: process.env.SENTRY_PROJECT,
                telemetry: false,
                disable: process.env.NODE_ENV !== 'production',
                release: {
                    name: RELEASE
                },
                debug: true
            })
        ]
    }
})
