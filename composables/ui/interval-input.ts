import { MaybeRef, reactiveComputed } from '@vueuse/core'
import { DateTime, Interval } from 'luxon'

type Options = {
    boundaries?: { start: Date; end: Date }
    minDuration?: number
    maxDuration?: number
    useMinMax?: boolean
}

export function useIntervalInput(start: Ref<Date>, end: Ref<Date>, options: MaybeRef<Options> = {}) {
    watchEffect(() => {
        if (start.value === null) {
            end.value = null
        }
    })

    const maxDate = new Date("9999-12-24T00:00")

    /**
     * ! This is _not_ a Luxon.Interval
     */
    const interval = computed(() => {
        return {
            start: start.value ? DateTime.fromJSDate(start.value) : null,
            end: end.value ? DateTime.fromJSDate(end.value) : null
        }
    })

    const boundaries = computed(() => {
        if (!unref(options).boundaries) {
            return null
        }

        const boundaries = Interval.fromDateTimes(
            //..
            unref(options).boundaries.start,
            unref(options).boundaries.end
        )

        if (!boundaries.isValid) {
            return null
        }

        return boundaries
    })

    const startAttrs = reactiveComputed(() => {
        const min = boundaries.value?.start
        const max = DateTime.min(
            //...
            ...[interval.value.end, boundaries.value?.end].filter((date) => !!date)
        )

        const attributes = {
            'modelValue': start.value,
            'onUpdate:modelValue': (value: Date) => (start.value = value),
            'min': min?.toJSDate(),
            'max': max?.toJSDate() || maxDate,
            'required': true
        }

        if (!unref(options).useMinMax) {
            attributes['min'] = null;
            attributes['max'] = maxDate;
        }

        return attributes
    })

    const endAttrs = reactiveComputed(() => {
        const min = DateTime.max(
            //...
            ...[interval.value.start, boundaries.value?.start].filter((date) => !!date)
        )

        const max = boundaries.value?.end

        const attributes = {
            'modelValue': end.value,
            'onUpdate:modelValue': (value: Date) => (end.value = value),
            'min': min?.toJSDate(),
            'max': max?.toJSDate() || maxDate,
            'disabled': !start.value,
            'isEndDate': true
        }

        if (!unref(options).useMinMax) {
            attributes['min'] = null;
            attributes['max'] = maxDate;
        }

        return attributes
    })

    const attrs = reactiveComputed(() => ({
        start: startAttrs,
        end: endAttrs
    }))

    function setStart(value: Date) {
        start
    }

    return {
        startAttrs,
        endAttrs
    }
}
