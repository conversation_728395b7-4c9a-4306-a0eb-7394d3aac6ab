import { h3ResponseBlobToBase64 } from '~/modules/api/server/utils/h3-response-blob-to-b64'

export default defineEventHandler(async (event) => {
    const { post } = await useApiProxy(event)

    const { reportType, fileType, dataBasis } = await readBody(event)

    console.log(reportType, fileType, dataBasis)

    const data = await h3ResponseBlobToBase64(post<any>(`/events/${event.context.params?.id}/reports`, { reportType, fileType, dataBasis }))

    let mimeType = 'application/pdf'

    if (fileType === 'csv') {
        mimeType = 'text/csv'
    } else if (fileType === 'xls') {
        mimeType = 'application/vnd.ms-excel'
    } else if (fileType === 'xlsx') {
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }

    return send(event, data, mimeType)

})
