<template>
    <input type="datetime-local" v-on="inputListeners" v-bind="inputAttrs" class="form-input h-10" max="9999-12-31T23:59"/>
</template>

<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'

    import { useDatetimeInput } from '~~/composables/ui/datetime-input'

    const props = defineProps<{
        modelValue: Date | null
        step?: number
    }>()

    const emit = defineEmits<{
        (event: 'update:modelValue', value: Date)
    }>()

    const model = useVModel(props, 'modelValue', emit)

    const { inputAttrs, inputListeners } = useDatetimeInput(model, {
        type: 'datetime',
        ...toRefs(props)
    })

</script>
