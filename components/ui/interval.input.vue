<template>
    <slot :start-input-attrs="startAttrs" :end-input-attrs="endAttrs">
        <UiFormField :label="label" v-bind="$attrs">
            <div class="flex flex-row items-center gap-x-2">
                <UiDatetimeInput v-bind="startAttrs" class="flex-1" />
                <span class="flex-none">bis</span>
                <UiDatetimeInput v-bind="endAttrs" class="flex-1" />
            </div>
        </UiFormField>
    </slot>
</template>

<script lang="ts" setup>
    import { useVModels } from '@vueuse/core'
    import { useIntervalInput } from '~~/composables/ui/interval-input'

    const props = withDefaults(
        defineProps<{
            start: Date | null
            end: Date | null
            boundaries?: { start: Date; end: Date }
            label?: string
            useMinMax?: boolean
        }>(),
        {
            label: 'Zeitraum auswählen',
            useMinMax: true
        }
    )

    const emit = defineEmits<{
        (event: 'update:start', value: Date): any
        (event: 'update:end', value: Date): any
    }>()

    const { start, end } = useVModels(props, emit)

    const options = computed(() => {
        return {
            boundaries: props.boundaries,
            useMinMax: props.useMinMax
        }
    })

    const { startAttrs, endAttrs } = useIntervalInput(start, end, options)
</script>
