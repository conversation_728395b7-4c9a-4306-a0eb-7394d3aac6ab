<template>
    <div>
        <form class="grid grid-cols-12 gap-4 p-4 bg-gray-50" @submit.prevent.stop="submitRegistration">
            <UiFormField label="Person wählen*" class="col-span-full md:col-span-5">
                <MemberPicker
                    v-if="!registration.resource.member"
                    @select="(member) => (registration.resource.member = member)"
                    :allowed-scopes="['MY_MEMBERS', 'GLOBAL_MEMBERS']"
                    popper-position="top-start"
                    class="w-full"
                    placeholder="Name, Personalnummer" />
                <div v-else class="flex items-center bg-white border rounded-sm">
                    <ProvideMember :member="registration.resource.member" class="flex-1 text-xs" />
                    <span @click="registration.resource.member = null" class="form-button button-xs ml-2 mr-0.5 flex-none rounded-full p-1">
                        <UiIcon name="x" class="w-4 h-4" />
                    </span>
                </div>
                <button @click="emit('registerExternalPerson')" class="text-sm text-sky-500 no-underline hover:underline cursor-pointer pt-2">Extern Mitwirkende hinzufügen</button>
            </UiFormField>

            <div class="col-span-full md:col-span-3 flex flex-col gap-11">
                <UiFormField label="Anwesend von">
                    <UiDatetimeInput v-model="registration.start" class="w-full" />
                </UiFormField>
                <UiCheckInput label='kommt jetzt' v-model="setDateFromToNow" class="-mt-9 md:flex col-span-3 col-start-5"/>
            </div>
             <div class="col-span-full md:col-span-3 flex flex-col gap-11">
                <UiFormField label="Anwesend bis" class="w-full">
                    <UiDatetimeInput v-model="registration.end" class="w-full" />
                </UiFormField>
                <UiCheckInput label='geht jetzt' v-model="setDateUpToToNow" class="-mt-9 md:flex col-span-3 col-start-5"/>
            </div>
            <button type="submit" class="col-span-full md:col-span-1 hover:cursor-pointer flex justify-center items-center mt-2 md:-mt-3 md:ml-2 self-center md:self-[unset] p-2.5 text-blue-500 hover:bg-blue-50 disabled:text-gray-300" :disabled="v$.$invalid">
                <UiIcon name="plus" />
            </button>
        </form>
    </div>
</template>


<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { required, helpers } from '@vuelidate/validators'

    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const emit = defineEmits<{
        (event: 'submit', value: ApiModel<'EventRegistration'>): any
        (event: 'registerExternalPerson'): any
    }>()

    const registration = ref(useModelFactory('EventRegistration').create({
      resource: useModelFactory("Resource").create({
        external: false,
        type: "PERSONAL"
      })
    }))

    const endOrStartNotEmpty = helpers.withMessage(
        'End or start date must be not empty.',
        () => {
            return !!registration.value.end || !!registration.value.start
        }
    )

    const v$ = useVuelidate<ApiModel<'EventRegistration'>>(
        {
            resource: {
               member: { required },
            },
            endOrStartNotEmpty
        },
        registration
    )

    const updateDateFromNowInterval = ref<number | null>(null);
    const updateDateUpToInterval = ref<number | null>(null);

    const setDateFromToNow = computed<boolean>({
        get() {
            return false
        },
        set(value){
            if(value) {
                updateDateFromNowInterval.value = window.setInterval(()=>{
                    registration.value.start = new Date()
                }, 10)
                registration.value.end = null
            } else {
                if (updateDateFromNowInterval.value !== null) {
                    clearInterval(updateDateFromNowInterval.value);
                 }
                updateDateFromNowInterval.value = null;
                registration.value.start = null
            }
            return value
        }
    })

    const setDateUpToToNow = computed<boolean>({
        get() {
            return false
        },
        set(value){
            if(value) {
                updateDateUpToInterval.value = window.setInterval(()=>{
                    registration.value.end = new Date()
                }, 10)
            } else {
                if (updateDateUpToInterval.value !== null) {
                    clearInterval(updateDateUpToInterval.value);
                 }
                updateDateUpToInterval.value = null;
                registration.value.end = null
            }
            return value
        }
    })

    onUnmounted(() => {
        if (updateDateFromNowInterval.value !== null) {
            clearInterval(updateDateFromNowInterval.value);
        }
        if (updateDateUpToInterval.value !== null) {
            clearInterval(updateDateUpToInterval.value);
        }
    });

    function submitRegistration() {
        if (v$.value.$invalid) {
            return
        }

        emit('submit', registration.value)

        registration.value = useModelFactory('EventRegistration').create({
          resource: useModelFactory("Resource").create({
              external: false,
              type: "PERSONAL"
            }),
            start: registration.value.start,
            end: registration.value.end
        })
    }

</script>
