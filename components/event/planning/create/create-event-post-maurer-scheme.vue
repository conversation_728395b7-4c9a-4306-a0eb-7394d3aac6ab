<template>
    <component :is="asComponent" :is-revealed="isRevealed" @cancel="cancelAndReset">
        <template #title>Maurer-Schema</template>

        <template v-if="isRevealed">
            <EventPlanningCreateMaurerScheme v-model="eventPosts" v-model:isFirstStep="isFirstStep" v-model:risk="risk" />
        </template>

        <template #footer>
            <button v-if="!isFirstStep" @click="resetValues" class="form-button button-contained-secondary mr-auto">Zurück</button>
            <button @click="cancelAndReset" class="form-button button-contained-secondary">Abbrechen</button>
            <button
                v-if="risk < 30"
                class="form-button button-sm button-contained disabled:pointer-events-none disabled:opacity-50"
                :disabled="eventPosts.length === 0 || isFirstStep"
                @click="submit">
                Speichern
            </button>
            <button v-else class="form-button button-contained" @click="cancelAndReset">Neu planen</button>
        </template>
    </component>
</template>

<script lang="ts" setup>
    import { UiBottomSheet, UiComposer } from '#components'
    import { type DialogController } from '~~/composables/dialog-controller'
    const props = defineProps<{
        as?: 'bottom-sheet' | 'composer'
        controller: DialogController<'applyMaurerScheme'>
    }>()

    const asComponent = computed(() => {
        const { as = 'composer' } = props
        if (as === 'bottom-sheet') {
            return UiBottomSheet
        } else {
            return UiComposer
        }
    })

    function cancelAndReset() {
        resetValues()
        cancel()
    }
    const eventPosts = ref<ApiModel<'EventPost'>[]>([])

    const isFirstStep = ref(true)
    const risk = ref(null)

    function resetValues() {
        isFirstStep.value = true
        risk.value = null
        eventPosts.value = []
    }

    const { isRevealed, cancel, confirm } = props.controller

    function submit() {
        confirm(eventPosts.value)
        resetValues()
    }
</script>
