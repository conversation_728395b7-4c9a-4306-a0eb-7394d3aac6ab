<script lang="ts" setup>
    import { useVModel } from '@vueuse/core'
    import { omit, range } from 'lodash-es'
    import {
        calcHelpers,
        calcPatientTransportVehicles,
        calcAmbulancesAmount,
        calcEmergencyDoctors,
        weighting,
        calcPointsForMaxVisitors
    } from '~/composables/event-posts'

    type Props = {
        modelValue: ApiModel<'EventPost'>[]
        isFirstStep: boolean
        risk: number | null
    }
    const props = defineProps<Props>()

    type Emit = {
        (e: 'update:modelValue', value: Props['modelValue']): void
        (e: 'update:isFirstStep', value: Props['isFirstStep']): void
        (e: 'update:risk', value: Props['risk']): void
    }
    const emit = defineEmits<Emit>()

    const isFirstStep = toRef(props, 'isFirstStep')

    const { event } = inject(EventPlanningKey)
    /**
     * Prepare input variables
     */

    const maxVisitors = ref<number>(null)
    const expectedVisitors = ref<number>(null)
    const vipPersonen = ref<number>(null)
    const eventType = ref(null)
    const isClosedEnvironment = ref(false)
    const higherViolenceProbability = ref(false)
    const interval = ref({ start: event.value.dateFrom, end: event.value.dateUpTo })

    /**
     * Prepare variables to display suggested results after risk calculation
     */

    const suggestedHelpers = ref(null)
    const suggestedPatientTransportVehicles = ref(null)
    const suggestedAmbulancesAmount = ref(null)
    const suggestedEmergencyDoctors = ref(null)
    const suggestedLargeAmbulances = ref(null)
    const operationsManagement = ref(null)
    const accidentEmergencyService = ref(null)

    /**
     * Prepare variables that user can manipulate
     */

    const helpers = ref(null)
    const patientTransportVehicles = ref(null)
    const ambulancesAmount = ref(null)
    const emergencyDoctors = ref(null)

    /**
     * Calculate suggested values to be displayed on second step
     */

    function calculateSuggestedValues() {
        helpers.value = suggestedHelpers.value = calcHelpers(risk.value)
        patientTransportVehicles.value = suggestedPatientTransportVehicles.value = calcPatientTransportVehicles(risk.value)
        ambulancesAmount.value = suggestedAmbulancesAmount.value = calcAmbulancesAmount(risk.value)
        emergencyDoctors.value = suggestedEmergencyDoctors.value = calcEmergencyDoctors(risk.value)
        suggestedLargeAmbulances.value = risk.value < 90.1 ? 0 : 1
        accidentEmergencyService.value = risk.value > 50 ? (risk.value > 110 ? 3 : risk.value < 80.1 ? 1 : 2) : null
        operationsManagement.value =
            risk.value > 30 ? (risk.value > 60 ? 'voll stabsmäßige Einsatzleitung' : 'stabsmäßige Einsatzleitung mit reduzierter Besatzung') : null
    }

    /**
     * Calculate the risk according to which, the warning will be displayed
     */
    const risk = useVModel(props, 'risk', emit)

    function calculateRisk() {
        const maxVisitorPoints = calcPointsForMaxVisitors(maxVisitors.value, isClosedEnvironment.value)
        const expVisitorsPoints = Math.floor((expectedVisitors.value - 1) / 500) + 1
        const vipVisitorsPoints =
            vipPersonen.value === 0 || vipPersonen.value === null ? 0 : vipPersonen.value > 10 ? 30 : (Math.floor((vipPersonen.value - 1) / 5) + 1) * 10

        risk.value = (expVisitorsPoints + maxVisitorPoints) * eventType.value.value + vipVisitorsPoints + 10 * Number(higherViolenceProbability.value)
        emit('update:isFirstStep', false)
    }

    watch(risk, () => calculateSuggestedValues())
    /**
     * Assign value to warning depending on the calculated risk
     */

    const warning = computed(() => {
        if (!risk.value || risk.value <= 30) return null
        if (risk.value <= 50)
            return 'Das Maurer-Schema empfiehlt eine stabsmäßige Einsatzleitung einzurichten. Grund ist das hohe Gesamtrisiko des Ereignisses. Die Planstellen musst du nach den jeweiligen örtlichen Vorgaben erstellen, das kann der drkserver nicht automatisiert für dich übernehmen.'
        if (risk.value <= 90)
            return 'Das Maurer-Schema empfiehlt eine stabsmäßige Einsatzleitung und Unfallhilfsstellen einzurichten. Grund ist das hohe Gesamtrisiko des Ereignisses. Die Planstellen musst du nach den jeweiligen örtlichen Vorgaben erstellen, das kann der drkserver nicht automatisiert für dich übernehmen.'
        return 'Das Maurer-Schema empfiehlt eine stabsmäßige Einsatzleitung, Unfallhilfsstellen einzurichten und Großraumrettungswagen einzusetzen. Grund ist das hohe Gesamtrisiko des Ereignisses. Die Planstellen musst du nach den jeweiligen örtlichen Vorgaben erstellen, das kann der drkserver nicht automatisiert für dich übernehmen.'
    })

    /**
     * Make sure that user can not write float number or epmty string
     */

    const preventInputValueToBeEmptyString = (event: InputEvent) => {
        const inputElement = event.target as HTMLInputElement
        let tempValue = inputElement.value
        // Remove leading zeros
        tempValue = tempValue.replace(/^0+/, '')
        // Remove non-digit characters
        tempValue = tempValue.replace(/\D/g, '')
        vipPersonen.value = Number(tempValue)
    }

    /**
     * Create variable to see if all needed values are filled
     */

    const isDataFilled = computed(() => {
        return (!!maxVisitors.value && !!expectedVisitors.value && (vipPersonen.value === null || !!vipPersonen.value || vipPersonen.value === 0) && !!eventType.value)
    })

    /**
     * Build default EventPost
     */
    function buildEventPost(eventPostBaseInfo: any) {
        const data: Partial<ApiModel<'EventPost'>> = {
            eventPostResourceType: 'PERSONAL',
            desiredValue: 1,
            dateFrom: event.value.dateFrom,
            dateUpTo: event.value.dateUpTo,
            driverLicenseCriteriaList: [],
            qualifications: [],
            technicArticleTypes: [],
            technicArticleUnitTypes: [],
            technicArticleUnitCategories: [],
            ...eventPostBaseInfo
        }

        return useModelFactory('EventPost').create(data)
    }

    function buildIndividualEventPosts(count: number, attributes: Partial<ApiModel<'EventPost'>>) {
        const eventPosts = range(count).map(() => {
            // Create as many individual models as needed
            return buildEventPost(attributes)
        })

        return eventPosts
    }

    /**
     * Build eventPosts for specific 'groups'. This is based on variables that user can manipulate on the
     * second step, after displaying suggested number of entities for specific group.
     * Recalculated when corresponding final variable (displayed on second step inputs) is changed.
     */

    const helpersEventPosts = computed(() => {
        return buildIndividualEventPosts(helpers.value, helperEventBody)
    })

    const emergencyDoctorsEventPosts = computed(() => {
        return buildIndividualEventPosts(emergencyDoctors.value, emergencyDoctorEventBody)
    })

    const patientTransportVehiclesEventPosts = computed(() => {
        return [
            // added dateFrom and dateUpTo props because its THECHNIC eventPost, which means interval is from start to end of the event
            // ?? This is obviusly done in `buildEventPost`
            ...buildIndividualEventPosts(patientTransportVehicles.value, transportVehicleEventBody.ambulance),
            ...buildIndividualEventPosts(patientTransportVehicles.value, transportVehicleEventBody.paramedic),
            ...buildIndividualEventPosts(patientTransportVehicles.value, transportVehicleEventBody.driver)
        ]
    })
    const ambulancesAmountEventPosts = computed(() => [
        // added dateFrom and dateUpTo props because its THECHNIC eventPost, which means interval is from start to end of the event
        // ?? This is obviusly done in `buildEventPost`
        ...buildIndividualEventPosts(ambulancesAmount.value, emergencyAmbulanceEventBody.ambulance),
        ...buildIndividualEventPosts(ambulancesAmount.value, emergencyAmbulanceEventBody.paramedic),
        ...buildIndividualEventPosts(ambulancesAmount.value, emergencyAmbulanceEventBody.emergencyParamedic)
    ])

    /**
     * Join the groups into one array of EventPost and update v-model
     */

    const eventPosts = useVModel(props, 'modelValue', emit)

    watch([helpers, patientTransportVehicles, ambulancesAmount, emergencyDoctors, interval, isFirstStep], () => {
        eventPosts.value = [
            ...helpersEventPosts.value,
            ...emergencyDoctorsEventPosts.value,
            ...patientTransportVehiclesEventPosts.value,
            ...ambulancesAmountEventPosts.value
        ]
    })

    const restrictedRange = { start: event.value.dateFrom, end: event.value.dateUpTo }
</script>

<template>
    <div v-if="isFirstStep" class="mb-4">
        <div>Gib hier die Eckdaten deines Ereignisses ein.</div>
        <div class="mt-2 text-sm">Das Maurer-Schema errechnet dir daraus einen Vorschlag, welches Personal und Material du einplanen solltest.</div>

        <div>
            <UiFormField class="mt-10 w-72" label="Zugelassene Besucher*innen *">
                <input type="number" min="1" placeholder="Zahl eingeben" v-model="maxVisitors" class="form-input box-border w-full" />
            </UiFormField>
            <UiFormField class="mt-2 w-72" label="Erwartete/tatsächliche Besucher*innen *">
                <input type="number" min="1" placeholder="Zahl eingeben" v-model="expectedVisitors" class="form-input box-border w-full" />
            </UiFormField>
            <UiFormField class="mt-2 w-72" label="VIPs *">
                <input type="number" min="0" placeholder="keine" v-model="vipPersonen" @input="preventInputValueToBeEmptyString" class="form-input box-border w-full" />
            </UiFormField>
            <UiFormField class="mt-2 w-72" label="Art der Veranstaltung *">
                <select class="form-select w-full" v-model="eventType" placeholder="Bitte auswählen">
                    <option v-for="option in weighting" :value="option" :key="option.label" >{{ option.label }}</option>
                </select>
            </UiFormField>

            <UiSwitch v-model="isClosedEnvironment" class="mb-2 mt-6" label="findet in geschlossener baulicher Anlage statt" />
            <UiSwitch v-model="higherViolenceProbability" class="mt-6" label="gewaltbereite Besucher*innen bekannt" />
        </div>

        <button
            class="form-button button-contained button-sm ml-auto mt-6 disabled:pointer-events-none disabled:opacity-50"
            :disabled="!isDataFilled"
            @click="calculateRisk()">
            Risiko berechnen
        </button>
    </div>

    <div v-else>
        <div>Ergebnis der Berechnung</div>
        <div class="mt-8 flex items-center justify-between">
            <div class="text-sm">Das Gesamtrisiko beträgt:</div>
            <div class="bg-green-500 px-4 py-2 font-bold text-green-900">{{ risk.toFixed(1) }} Punkte</div>
        </div>
        <div class="mt-10 text-sm">
            Um das Ereignis zu sichern <br />
            empfiehlt das Maurer-Schema dir:
        </div>
        <div class="mt-6 text-sm font-bold">
            <div v-if="suggestedHelpers">- {{ suggestedHelpers }} Sanitäter*innen</div>
            <div v-if="suggestedPatientTransportVehicles">- {{ suggestedPatientTransportVehicles }} Krankentransportwagen (KTW)</div>
            <div v-if="suggestedAmbulancesAmount">- {{ suggestedAmbulancesAmount }} Rettungswagen (RTW)</div>
            <div v-if="suggestedEmergencyDoctors">- {{ suggestedEmergencyDoctors }} Notärzt*innen</div>
            <div v-if="suggestedLargeAmbulances">- {{ suggestedLargeAmbulances }} Großraumrettungswagen (GRTW)</div>
            <div v-if="operationsManagement">- Einsatzleitung: {{ operationsManagement }}</div>
            <div v-if="accidentEmergencyService">- Unfallhilfsstellen (UHS): {{ accidentEmergencyService }}</div>
        </div>

        <div v-if="warning" class="bg-softred-500 relative mt-8 flex rounded px-4 py-4 text-sm text-white">
            <IconExclamationCircle class="absolute h-6 w-6" />
            <div class="ml-8">
                {{ warning }}
            </div>
        </div>

        <div v-else class="mb-4">
            <div class="mb-4 mt-8">In die Planung übernehmen</div>
            <UiFormField class="mt-5 w-64" label="Sanitäter*innen">
                <input type="number" min="0" v-model="helpers" class="form-input box-border w-full" />
            </UiFormField>
            <UiFormField class="mt-5 w-64" label="Krankentransportwagen (KTW)">
                <input type="number" min="0" v-model="patientTransportVehicles" class="form-input box-border w-full" />
            </UiFormField>
            <UiFormField class="mt-5 w-64" label="Rettungswagen (RTW)">
                <input type="number" min="0" v-model="ambulancesAmount" class="form-input box-border w-full" />
            </UiFormField>
            <UiFormField class="mt-5 w-64" label="Notärzt*innen">
                <input type="number" min="0" v-model="emergencyDoctors" class="form-input box-border w-full" />
            </UiFormField>

            <UiIntervalInput
                label="Zeitraum *"
                class="mt-5"
                v-model:start="interval.start"
                v-model:end="interval.end"
                :boundaries="restrictedRange" />
        </div>
    </div>
</template>
