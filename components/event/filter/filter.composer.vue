<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Filter anpassen</template>

        <form class="sm:mb-4 flex w-full flex-col" @keydown.enter.prevent>
            <div class="flex-1">
                <UiFormField label="Zeitraum" class="mb-4">
                    <UiIntervalInput v-model:start="model.start" v-model:end="model.end">
                        <template #default="{ startInputAttrs, endInputAttrs }">
                            <div class="grid grid-cols-2 sm:flex sm:flex-row w-full items-start sm:items-center gap-2 relative" :class="startInputAttrs.modelValue === null ? 'mb-[45px]' : ''">
                                <div class="col-span-1 flex flex-col sm:flex-1" >
                                    <UiDateInput v-bind="startInputAttrs" />
                                    <span
                                        v-if="model.start === null"
                                        class="absolute top-[45px] text-sm text-red-500">Falsches Datum</span>
                                </div>
                                <p class="col-span-2">bis</p>
                                <div class="col-span-1 flex flex-1 ">
                                    <UiDateInput v-bind="endInputAttrs" class="sm:flex-1" />
                                </div>
                                <button @click.prevent.stop="() => (model.end = null)" class="h-full w-fit col-start-2 col-span-1 flex items-center form-button button-sm rounded-md">
                                    <UiIcon name="trash" class="h-4 w-4" />
                                </button>
                            </div>
                        </template>
                    </UiIntervalInput>
                </UiFormField>

                <UiFormField label="Art/Typ" class="mb-4">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <UiRadioInput label="Alle" v-model="type" value="ALL" />
                        <UiRadioInput label="Dienste/Übungen/Einsätze" v-model="type" value="EVENT" />
                        <UiRadioInput label="Ausbildungen" v-model="type" value="APPRENTICESHIP" />
                    </div>
                </UiFormField>

                <fieldset class="mb-4 flex flex-col gap-y-4">
                    <legend class="text-grey-900 mb-2 block text-xs leading-4 empty:hidden">Ereignisstatus</legend>
                     <div class="flex flex-wrap">
                        <button
                            type="button"
                            v-for="({label, key}) in validStatus"
                            :key="key"
                            class="mb-2 mr-2 rounded-full bg-gray-50 px-2 py-1 text-sm text-gray-500 hover:cursor-pointer hover:bg-gray-200"
                            @click="toogleStatus(key)"
                            :class="{ '!bg-gold-100': isStatusSelected(key) }">
                            {{ label }}
                        </button>
                    </div>
                </fieldset>

                <CodeEntryInput
                    placeholder="Text eingeben"
                    v-model="extendedDescription"
                    list-type="EventExtendedDescription"
                    label="Bezeichnung / Nähere Bezeichnung"
                    class="mb-4" />

                <UiFormField label="Gliederungen" class="mb-4 flex flex-col">
                    <UiRadioInput label="Alle Gliederungen" v-model="executedBy" value="ALL" class="mb-4" />
                    <UiRadioInput label="Gliederungen, in denen ich aktiv mitwirke" v-model="executedBy" value="MY" class="mb-4" />
                    <UiRadioInput label="Diese Gliederungen:" v-model="executedBy" value="SELECTED" class="w-full">
                        <UiComboboxBatch
                            v-if="executedBy === 'SELECTED'"
                            label=""
                            placeholder="Für Vorschläge tippen"
                            v-model="selected"
                            :mutation="organizationsMutation"
                            :limit="25"
                            :showSummary="true"
                            :get-option-label="(item)=>{return Boolean(item.value) ? item.value.name : ''}"
                            :initial-loading="false"
                            :clearAfterSelecting="false"
                            :get-option-disabled="(item) => getDisabled(item)"
                            class="mt-1"
                        >
                            <template #item="itemProps">
                                <EventSharingCommunitySearchItem
                                    :selected="itemProps.selected"
                                    :active="itemProps.active"
                                    :option="itemProps.option"
                                />
                            </template>
                        </UiComboboxBatch>
                    </UiRadioInput>
                </UiFormField>

               <UiFormField label="Meine Rückmeldung" class="mb-4 flex flex-col">
                    <UiRadioInput label="Alle" v-model="responseStatus" value="ALL" class="mb-4">
                    </UiRadioInput>
                    <UiRadioInput label="Bin eingeladen" v-model="responseStatus" value="INVITED" class="mb-4">
                        Ereignisse, bei denen ich noch keine Rückmeldung gegeben habe
                    </UiRadioInput>
                    <UiRadioInput label="Bin verfügbar" v-model="responseStatus" value="CONFIRMED" class="mb-4">
                        Ereignisse, bei denen ich verfügbar oder teilweise verfügbar gemeldet bin
                    </UiRadioInput>
                    <UiRadioInput label="Bin nicht verfügbar" v-model="responseStatus" value="DENIED" class="mb-4">
                        Ereignisse, bei denen ich nicht verfügbar gemeldet bin
                    </UiRadioInput>
                    <UiRadioInput label="Habe mich gemeldet" v-model="responseStatus" value="ANSWERED" class="mb-4">
                        Ereignisse, bei denen bereits eine Rückmeldung eingetragen ist
                    </UiRadioInput>
                </UiFormField>

                <fieldset class="mb-4 flex flex-col gap-y-4">
                    <legend class="text-grey-900 mb-2 block text-xs leading-4 empty:hidden">Für Helfende</legend>

                    <UiCheckInput label="Planstellen noch offen" v-model="withOpenEventPosts">
                        Ereignisse mit Planstellen, die noch nicht besetzt sind
                    </UiCheckInput>

                    <UiCheckInput label="Bin zugeordnet" v-model="assignedToEventPost">
                        Ereignisse, bei denen ich auf einer Planstelle stehe
                    </UiCheckInput>
                </fieldset>

                <fieldset class="mb-4 flex flex-col gap-y-4">
                    <legend class="text-grey-900 mb-2 block text-xs leading-4 empty:hidden">Für Planende</legend>

                    <UiCheckInput label="Darf registrieren" v-model="registratorFor">
                        Ereignisse, bei denen ich Einsatzzeiten von Helfer*innen erfassen darf
                    </UiCheckInput>

                    <UiCheckInput label="Bin zugeordnet mit erweiterten Rechten" v-model="withExtendedPermissions">
                        Ereignisse, bei denen ich auf einer Planstelle stehe, die erweiterte Rechte hat
                    </UiCheckInput>

                    <UiCheckInput label="Bin verantwortlich" v-model="responsibleFor">
                        Ereignisse, bei denen ich Ereignismanager*in oder Ereignisverantwortliche*r bin
                    </UiCheckInput>

                    <UiCheckInput label="Dienstweg" v-model="inChainOfCommand">
                        Ereignisse, bei denen ich dem Dienstweg zustimmen muss
                    </UiCheckInput>
                </fieldset>
            </div>
        </form>

        <template #footer>
                <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
                <button class="form-button button-contained" :disabled="model.start === null ? true : false" @click="confirm(model)">Filter anwenden</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import type { EventFilter } from '~~/composables/event-filter'
    import { syncRef } from '@vueuse/core'
    import { DialogController } from '~~/composables/dialog-controller'
    import { clone } from 'lodash-es'
    import { DateTime } from 'luxon'

    const props = defineProps<{
        controller: DialogController<'adjustFilterSettings'>
        filter: EventFilter
    }>()


    const { organizationsMutation } = useEventSharingExtractedOrganisation()

    const { isRevealed, onReveal, confirm, cancel, onConfirm } = props.controller

    const model = ref<EventFilter>(null)

    onReveal(()=>{
        syncRef(toRef(props, 'filter'), model, {
            direction: 'ltr',
            transform: {
                ltr: (filter) => clone(filter)
            }
        })

        if(!props.filter.executedBy) {
            selected.value = []
        }
        if(props.filter.executedBy && props.filter.executedBy?.length !== selected.value.length){

            const allowedIds = new Set(props.filter.executedBy.map(item => item.id));

            selected.value = selected.value.filter(item =>
                allowedIds.has(item.id)
            );
        }
    })

    function preventDateBeingNull(model: EventFilter): EventFilter{
        const today = DateTime.now()
        if (model.start === null && model.end === null) {
                model.start = today.minus({ days: 7 }).startOf('day').toJSDate()
                model.end = today.plus({ days: 30 }).endOf('day').toJSDate()
        }
        return model;
    }

    onConfirm(preventDateBeingNull)

    const {
        type,
        withOpenEventPosts,
        withExtendedPermissions,
        responsibleFor,
        assignedToEventPost,
        inChainOfCommand,
        executedBy,
        organisations,
        extendedDescription,
        registratorFor,
        responseStatus,
        status
        } = useEventFilterFacade(model)

    const selected = ref([])

    function getDisabled(item: any) {
      return Boolean(item.value) ? !item.value.active : false
    }

    watch(selected, ()=>{
        if(selected.value.length >= 0) {
            const processedSelected = selected.value.map(element => {
                return {
                    id: element.id ? element.id : element.value.id,
                    label: element.label ? element.label : element.value.name
                }
            })
            organisations.value = processedSelected
        }
        if(selected.value.length === 0) {
            organisations.value = null
        }
    })

    type StatusKey = 'FINISHED' | 'CANCELED' | 'APPROVED' | 'WAITING_FOR_APPROVAL'

    const validStatus: Array<{ label: string; key: StatusKey | 'all' }> = [
        { label: 'Alle', key: 'all' },
        { label: 'Noch nicht bestätigt', key: 'WAITING_FOR_APPROVAL' },
        { label: 'Bestätigt', key: 'APPROVED' },
        { label: 'Abgesagt', key: 'CANCELED' },
        { label: 'Abgeschlossen', key: 'FINISHED' }
    ]

    function selectAllStatuses() {
        if (status.value.length === validStatus.length - 1) {
            status.value = []
        } else {
            status.value = validStatus.filter(s => s.key !== 'all').map(s => s.key as StatusKey)
        }
    }

    function toogleStatus(eventStatus: StatusKey | 'all') {
        if (eventStatus === 'all') {
            selectAllStatuses()
        } else {
            if (status.value.includes(eventStatus)) {
                status.value = status.value.filter(s => s !== eventStatus)
            } else {
                status.value = [...status.value, eventStatus]
            }
        }
    }

    function isStatusSelected(key: StatusKey | 'all') {
        return key === 'all' ? status.value.length === validStatus.length - 1 : status.value.includes(key)
    }

</script>
