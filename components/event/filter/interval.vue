<template>
    <UiFormField label="Zeitraum">
        <select v-model="selectedInterval" class="form-select w-full">
            <option v-for="{ key, label, value } in options" :key="key" v-text="label" :value="value" />
        </select>
    </UiFormField>
</template>

<script lang="ts" setup>
    import type { EventFilter } from '~~/composables/event-filter'

    import { isEqual, uniqueId } from 'lodash-es'
    import { DateTime, Interval } from 'luxon'

    const { $formatDate } = useNuxtApp()

    const modelValue = defineModel<EventFilter>()

    const options = computed(() => {
        const today = DateTime.now()

        const defaultOptions = [
            {
                key: uniqueId('interval-'),
                label: 'Letzte 90 Tage',
                value: {
                    start: today.minus({ days: 90 }).startOf('day').toJSDate(),
                    end: today.endOf('day').toJSDate()
                }
            },
            {
                key: uniqueId('interval-'),
                label: 'Letzte 30 Tage',
                value: {
                    start: today.minus({ days: 30 }).startOf('day').toJSDate(),
                    end: today.endOf('day').toJSDate()
                }
            },
            {
                key: uniqueId('interval-'),
                label: '-7/+30 Tage',
                value: {
                    start: today.minus({ days: 7 }).startOf('day').toJSDate(),
                    end: today.plus({ days: 30 }).endOf('day').toJSDate()
                }
            },
            {
                key: uniqueId('interval-'),
                label: 'Nächste 30 Tage',
                value: {
                    start: today.startOf('day').toJSDate(),
                    end: today.plus({ days: 30 }).endOf('day').toJSDate()
                }
            },
            {
                key: uniqueId('interval-'),
                label: 'Nächste 90 Tage',
                value: {
                    start: today.startOf('day').toJSDate(),
                    end: today.plus({ days: 90 }).endOf('day').toJSDate()
                }
            }
        ]

        if (!findMatchingInterval(defaultOptions.map(({ value }) => value))) {
            let label = null

            if (modelValue.value.start && modelValue.value.end) {
                label = `${$formatDate(modelValue.value.start, 'short')} bis ${$formatDate(modelValue.value.end, 'short')}`
            } else if (modelValue.value.start) {
                label = `ab ${$formatDate(modelValue.value.start, 'short')}`
            } else if (modelValue.value.end) {
                label = `bis ${$formatDate(modelValue.value.end, 'short')}`
            } else {
                label = 'Keine Einschränkung'
            }

            defaultOptions.unshift({
                key: uniqueId('interval-'),
                label,
                value: {
                    start: modelValue.value.start || null,
                    end: modelValue.value.end || null
                }
            })
        }

        return defaultOptions
    })

    function findMatchingInterval(options: { start: Date; end: Date }[]) {
        return options?.find(({ start, end }) => {
            return isEqual(start, modelValue.value.start) && isEqual(end, modelValue.value.end)
        })
    }

    const selectedInterval = computed({
        get() {
            return findMatchingInterval(options.value.map(({ value }) => value))
        },
        set({ start, end }) {
            modelValue.value = {
                ...modelValue.value,
                start,
                end
            }
        }
    })
</script>
