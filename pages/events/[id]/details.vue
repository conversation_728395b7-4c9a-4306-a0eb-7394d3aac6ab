<template>
    <div>
        <EventDetailGeneralPanel :event="event" class="max-w-[36rem]" />

        <hr class="border-t-grey-100 my-6" />

        <EventDetailCostsPanel :event="event" class="max-w-[36rem]" />

        <hr class="border-t-grey-100 my-6" />

        <EventDetailRemarksPanel :event="event" class="max-w-[36rem]" />

        <hr class="border-t-grey-100 my-6" />

        <EventDocumentsPanel :event="event" />

        <hr class="border-t-grey-100 my-6" />

        <EventSidebarPublicRemarkPanel :event="event" />

        <hr class="border-t-grey-100 my-6" />

        <EventSidebarLocationsPanel :event="event" />

        <hr class="border-t-grey-100 my-6" />

        <EventSidebarOrganizerPanel :event="event" />

        <hr class="border-t-grey-100 my-6" />

        <EventSidebarResponsiblesPanel :event="event" />
    </div>
</template>

<script setup lang="ts">
    defineProps<{
        event: ApiModel<'Event'>
    }>()

    // const checkableProps = ['dressCodes', 'caterings', 'internalEventRemark'] as const

    // function isCollectionField(prop: keyof ApiModel<'Event'>): prop is 'dressCodes' | 'caterings' {
    //     return ['dressCodes', 'caterings'].includes(prop)
    // }

    // function needsUpdate<T extends ApiModel<'Event'>>(previous: T, candidate: T, prop: (typeof checkableProps)[number]) {
    //     if (isCollectionField(prop)) {
    //         if (previous[prop]?.length !== candidate[prop]?.length || !!differenceBy(previous[prop], candidate[prop], 'id').length) {
    //             return true
    //         }

    //         return false
    //     }

    //     return false
    // }
</script>
